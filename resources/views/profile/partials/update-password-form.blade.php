<section>
    <div class="mb-4">
        <h6 class="fw-semibold mb-2">{{ __('Update Password') }}</h6>
        <p class="text-muted fs-12">{{ __('Ensure your account is using a long, random password to stay secure.') }}</p>
    </div>

    <form method="post" action="{{ route('password.update') }}">
        @csrf
        @method('put')

        <div class="row gy-3">
            <div class="col-xl-12">
                <label for="update_password_current_password" class="form-label">{{ __('Current Password') }} <span
                        class="text-danger">*</span></label>
                <input id="update_password_current_password" name="current_password" type="password"
                    class="form-control @error('current_password', 'updatePassword') is-invalid @enderror"
                    autocomplete="current-password">
                @error('current_password', 'updatePassword')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="col-xl-12">
                <label for="update_password_password" class="form-label">{{ __('New Password') }} <span
                        class="text-danger">*</span></label>
                <input id="update_password_password" name="password" type="password"
                    class="form-control @error('password', 'updatePassword') is-invalid @enderror"
                    autocomplete="new-password">
                @error('password', 'updatePassword')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="col-xl-12">
                <label for="update_password_password_confirmation" class="form-label">{{ __('Confirm Password') }} <span
                        class="text-danger">*</span></label>
                <input id="update_password_password_confirmation" name="password_confirmation" type="password"
                    class="form-control @error('password_confirmation', 'updatePassword') is-invalid @enderror"
                    autocomplete="new-password">
                @error('password_confirmation', 'updatePassword')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="col-xl-12">
                <div class="d-flex gap-2 align-items-center">
                    <button type="submit" class="btn btn-warning">
                        <i class="ti ti-check me-1"></i>{{ __('Save Changes') }}
                    </button>

                    @if (session('status') === 'password-updated')
                        <div class="alert alert-success mb-0 py-2 px-3" id="password-saved-message">
                            <i class="ti ti-check me-1"></i>{{ __('Saved.') }}
                        </div>
                        <script>
                            setTimeout(() => {
                                const msg = document.getElementById('password-saved-message');
                                if (msg) msg.style.display = 'none';
                            }, 3000);
                        </script>
                    @endif
                </div>
            </div>
        </div>
    </form>
</section>
