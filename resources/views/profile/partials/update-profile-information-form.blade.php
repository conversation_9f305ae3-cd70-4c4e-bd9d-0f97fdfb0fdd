<section>
    <div class="mb-4">
        <h6 class="fw-semibold mb-2">{{ __('Profile Information') }}</h6>
        <p class="text-muted fs-12">{{ __("Update your account's profile information and email address.") }}</p>
    </div>

    <form id="send-verification" method="post" action="{{ route('verification.send') }}">
        @csrf
    </form>

    <form method="post" action="{{ route('profile.update') }}">
        @csrf
        @method('patch')

        <div class="row gy-3">
            <div class="col-xl-12">
                <label for="name" class="form-label">{{ __('Name') }} <span class="text-danger">*</span></label>
                <input id="name" name="name" type="text"
                    class="form-control @error('name') is-invalid @enderror" value="{{ old('name', $user->name) }}"
                    required autofocus autocomplete="name">
                @error('name')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="col-xl-12">
                <label for="username" class="form-label">{{ __('Username') }}</label>
                <input id="username" name="username" type="text"
                    class="form-control @error('username') is-invalid @enderror"
                    value="{{ old('username', $user->username) }}" autocomplete="username">
                @error('username')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <div class="form-text">{{ __('Optional. Used for alternative login method.') }}</div>
            </div>

            <div class="col-xl-12">
                <label for="email" class="form-label">{{ __('Email') }} <span class="text-danger">*</span></label>
                <input id="email" name="email" type="email"
                    class="form-control @error('email') is-invalid @enderror" value="{{ old('email', $user->email) }}"
                    required autocomplete="email">
                @error('email')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror

                @if ($user instanceof \Illuminate\Contracts\Auth\MustVerifyEmail && !$user->hasVerifiedEmail())
                    <div class="alert alert-warning mt-2">
                        <p class="mb-2">{{ __('Your email address is unverified.') }}</p>
                        <button form="send-verification" class="btn btn-sm btn-warning">
                            {{ __('Click here to re-send the verification email.') }}
                        </button>
                        @if (session('status') === 'verification-link-sent')
                            <p class="mt-2 text-success fw-medium">
                                {{ __('A new verification link has been sent to your email address.') }}
                            </p>
                        @endif
                    </div>
                @endif
            </div>

            <!-- Role Display (Read-only) -->
            <div class="col-xl-12">
                <label for="role" class="form-label">{{ __('Account Role') }}</label>
                <div class="form-control bg-light">
                    @if ($user->role === 'admin')
                        <span
                            class="badge bg-danger">{{ ucfirst(str_replace('_', ' ', $user->role)) }}</span>
                    @elseif($user->role === 'normal_user')
                        <span
                            class="badge bg-primary">{{ ucfirst(str_replace('_', ' ', $user->role)) }}</span>
                    @else
                        <span
                            class="badge bg-success">{{ ucfirst(str_replace('_', ' ', $user->role)) }}</span>
                    @endif
                </div>
                <div class="form-text">{{ __('Contact an administrator to change your role.') }}</div>
            </div>

            <div class="col-xl-12">
                <div class="d-flex gap-2 align-items-center">
                    <button type="submit" class="btn btn-warning">
                        <i class="ti ti-check me-1"></i>{{ __('Save Changes') }}
                    </button>

                    @if (session('status') === 'profile-updated')
                        <div class="alert alert-success mb-0 py-2 px-3" id="saved-message">
                            <i class="ti ti-check me-1"></i>{{ __('Saved.') }}
                        </div>
                        <script>
                            setTimeout(() => {
                                const msg = document.getElementById('saved-message');
                                if (msg) msg.style.display = 'none';
                            }, 3000);
                        </script>
                    @endif
                </div>
            </div>
        </div>
    </form>
</section>
