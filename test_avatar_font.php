<?php

/**
 * Test script to verify Avatar package is using Inter Bold font
 * 
 * This script tests the avatar generation with the new Inter Bold font configuration.
 * Run this script to verify that the font configuration is working correctly.
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel application
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Avatar;

echo "Testing Avatar Generation with Inter Bold Font\n";
echo "==============================================\n\n";

// Test different names to see avatar generation
$testNames = [
    '<PERSON>',
    '<PERSON>', 
    'Admin User',
    'Test Member',
    'Sample User'
];

foreach ($testNames as $name) {
    echo "Generating avatar for: {$name}\n";
    
    try {
        // Generate avatar and get base64 data
        $avatarData = Avatar::create($name)->toBase64();
        
        // Check if it's a valid base64 image
        if (strpos($avatarData, 'data:image/png;base64,') === 0) {
            echo "✓ Avatar generated successfully (PNG format)\n";
            echo "  Data length: " . strlen($avatarData) . " characters\n";
            echo "  Preview: " . substr($avatarData, 0, 50) . "...\n";
        } else {
            echo "✗ Avatar generation failed - invalid format\n";
        }
    } catch (Exception $e) {
        echo "✗ Error generating avatar: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

// Test configuration
echo "Avatar Configuration:\n";
echo "====================\n";
$config = config('laravolt.avatar');
echo "Font files: " . implode(', ', $config['fonts']) . "\n";
echo "Font size: " . $config['fontSize'] . "\n";
echo "Shape: " . $config['shape'] . "\n";
echo "Width: " . $config['width'] . "px\n";
echo "Height: " . $config['height'] . "px\n";
echo "Characters: " . $config['chars'] . "\n";

// Check if font file exists
echo "\nFont File Check:\n";
echo "================\n";
foreach ($config['fonts'] as $fontPath) {
    if (file_exists($fontPath)) {
        echo "✓ Font file exists: {$fontPath}\n";
        echo "  File size: " . number_format(filesize($fontPath)) . " bytes\n";
    } else {
        echo "✗ Font file missing: {$fontPath}\n";
    }
}

echo "\nTest completed!\n";
